; Custom configurations for local environment
sendmail_path = "/usr/bin/msmtp -t --host=mailpit --port=1025 --from=<EMAIL>"
memory_limit = "1G"
output_buffering = "4096"
post_max_size = "64M"
short_open_tag = "Off"
upload_max_filesize = "64M"
allow_url_include = "Off"
auto_prepend_file = NULL
max_execution_time = 300
display_errors = "On"
display_startup_errors = "On"
error_reporting = E_ALL
log_errors = "On"
error_log = "/var/log/php/error.log"
date.timezone = "UTC"

; Xdebug
xdebug.mode=debug
xdebug.start_with_request=yes
xdebug.client_host=host.docker.internal
xdebug.client_port=9003
xdebug.discover_client_host=true
xdebug.log=/tmp/xdebug.log
