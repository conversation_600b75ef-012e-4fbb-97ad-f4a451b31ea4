uuid: 6422cf46-1a0d-4bcf-a06a-6656d6d3c309
langcode: en
status: true
dependencies:
  config:
    - field.field.node.city.field_average_rental_price
    - field.field.node.city.field_average_salary
    - field.field.node.city.field_business_environment
    - field.field.node.city.field_region
    - node.type.city
  module:
    - content_moderation
    - cshs
    - paragraphs
    - path
id: node.city.default
targetEntityType: node
bundle: city
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_average_rental_price:
    type: string_textfield
    weight: 12
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_average_salary:
    type: string_textfield
    weight: 13
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_business_environment:
    type: paragraphs
    weight: 14
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: business_environment
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        convert: '0'
        duplicate: duplicate
    third_party_settings: {  }
  field_region:
    type: cshs
    weight: 11
    region: content
    settings:
      save_lineage: false
      force_deepest: true
      parent: null
      level_labels: ''
      hierarchy_depth: 0
      required_depth: 0
      none_label: '- Please select -'
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  simple_sitemap:
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 10
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  entitygroupfield: true
  publish_on: true
  publish_state: true
  unpublish_on: true
  unpublish_state: true
