uuid: 4fcfbec9-7bdf-4fa4-b936-5072ffbde5b1
langcode: en
status: open
dependencies: {  }
weight: 0
open: null
close: null
uid: 1
template: false
archive: false
id: job_application
title: 'Apply for the job'
description: ''
categories: {  }
elements: |-
  flexbox1:
    '#type': webform_flexbox
    first_name:
      '#type': textfield
      '#title': 'First name'
      '#placeholder': 'First name'
      '#required': true
      '#default_value': '[current-user:talent:field_first_name]'
    last_name:
      '#type': textfield
      '#title': 'Last name'
      '#placeholder': 'Last name'
      '#required': true
      '#default_value': '[current-user:talent:field_last_name]'
    email:
      '#type': email
      '#title': Email
      '#readonly': true
      '#default_value': '[current-user:mail]'
  flexbox2:
    '#type': webform_flexbox
    linkedin:
      '#type': webform_link
      '#title': 'LinkedIn profile'
      '#default_value':
        url: '[current-user:talent:field_linkedin:uri]'
        title: ''
      '#title__access': false
      '#url__title': 'LinkedIn profile'
    phone_number:
      '#type': phone_number
      '#title': 'Phone number'
      '#default_value':
        value: '[current-user:talent:field_phone:value]'
        country: '[current-user:talent:field_phone:country]'
        extension: null
      '#default_country': BG
      '#allowed_types':
        1: '1'
      '#country__access': false
      '#local_number__access': false
      '#extension__access': false
    education:
      '#type': webform_term_select
      '#title': Education
      '#required': true
      '#default_value': '[current-user:talent:field_education:target_id]'
      '#vocabulary': education
    residence_place:
      '#type': textfield
      '#title': 'Where do I live now'
      '#default_value': '[current-user:talent:field_residence_place]'
  cv_options:
    '#type': radios
    '#title': 'Attach CV'
    '#options':
      existing: 'Choose from available CVs'
      new: 'Upload a new CV'
    '#options_display': side_by_side
    '#default_value': existing
  cv_existing:
    '#type': webform_entity_radios
    '#title': 'Available CVs'
    '#title_display': none
    '#options_display': buttons_horizontal
    '#states':
      required:
        ':input[name="cv_options"]':
          value: existing
      visible:
        ':input[name="cv_options"]':
          value: existing
    '#target_type': file
    '#selection_handler': views
    '#selection_settings':
      view:
        view_name: existing_cvs_on_apply
        display_name: entity_reference_1
        arguments:
          - '[current-user:uid]'
  cv_upload:
    '#type': managed_file
    '#title': 'Upload CV'
    '#title_display': none
    '#states':
      required:
        ':input[name="cv_options"]':
          value: new
      visible:
        ':input[name="cv_options"]':
          value: new
    '#max_filesize': '10'
  certificates_options:
    '#type': radios
    '#title': 'Attach certificates'
    '#options':
      existing: 'Choose from the available certificates'
      new: 'Upload new certificates'
    '#options_display': side_by_side
    '#default_value': existing
  certificates_existing:
    '#type': webform_entity_checkboxes
    '#title': 'Available certificates'
    '#title_display': none
    '#options_display': buttons_horizontal
    '#states':
      visible:
        ':input[name="certificates_options"]':
          value: existing
    '#target_type': file
    '#selection_handler': views
    '#selection_settings':
      view:
        view_name: existing_certificates_on_apply
        display_name: entity_reference_1
        arguments:
          - '[current-user:uid]'
  certificates_upload:
    '#type': managed_file
    '#title': 'Upload certificates'
    '#multiple': 5
    '#title_display': none
    '#states':
      visible:
        ':input[name="certificates_options"]':
          value: new
    '#max_filesize': '10'
    '#file_extensions': 'jpg jpeg png webp pdf doc docx'
  message:
    '#type': textarea
    '#title': 'Message / Cover letter to the company'
    '#placeholder': 'Start typing here...'
  actions:
    '#type': webform_actions
    '#title': 'Submit button(s)'
    '#submit__label': 'Send application'
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: false
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_open_message: ''
  form_close_message: ''
  form_exception_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_autofocus: false
  form_details_toggle: false
  form_reset: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_attributes: {  }
  form_method: ''
  form_action: ''
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_exception_message: ''
  submission_locked_message: ''
  submission_log: false
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  previous_submission_message: "<p>You've already applied to this position.</p>"
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: inline
  confirmation_url: ''
  confirmation_title: ''
  confirmation_message: '<p>You application has been received successfully.</p>'
  confirmation_attributes: {  }
  confirmation_back: false
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
