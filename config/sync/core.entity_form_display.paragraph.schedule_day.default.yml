uuid: 429dc104-2f7b-41c0-a33b-60e8e412f4f1
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.schedule_day.field_schedule_slot
    - field.field.paragraph.schedule_day.field_title
    - paragraphs.paragraphs_type.schedule_day
  module:
    - paragraphs
id: paragraph.schedule_day.default
targetEntityType: paragraph
bundle: schedule_day
mode: default
content:
  field_schedule_slot:
    type: paragraphs
    weight: 1
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed_expand_nested
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: schedule_slot
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        convert: '0'
        duplicate: duplicate
    third_party_settings: {  }
  field_title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  status: true
