uuid: 1caeac7e-cfb8-42b3-bf5d-31003b06a1f9
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.news.field_event
    - field.field.node.news.field_formatted_description
    - field.field.node.news.field_gallery
    - field.field.node.news.field_image_media
    - field.field.node.news.field_summary
    - node.type.news
  module:
    - user
id: node.news.teaser
targetEntityType: node
bundle: news
mode: teaser
content:
  field_image_media:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 2
    region: content
  field_summary:
    type: basic_string
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  node_read_time:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  field_event: true
  field_formatted_description: true
  field_gallery: true
  langcode: true
  links: true
  private_message_link: true
  reading_time: true
  search_api_excerpt: true
  toc_js: true
