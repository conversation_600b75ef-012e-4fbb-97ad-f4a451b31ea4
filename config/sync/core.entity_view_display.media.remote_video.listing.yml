uuid: 418acbab-c135-40bd-a259-f1efc793930e
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.listing
    - field.field.media.remote_video.field_category
    - field.field.media.remote_video.field_description_long
    - field.field.media.remote_video.field_media_oembed_video
    - media.type.remote_video
  module:
    - bwy_general
id: media.remote_video.listing
targetEntityType: media
bundle: remote_video
mode: listing
content:
  field_category:
    type: entity_reference_label
    label: hidden
    settings:
      link: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_description_long:
    type: basic_string
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: content
  field_media_oembed_video:
    type: bwy_general_remote_video
    label: hidden
    settings:
      max_width: '680'
      max_height: '676'
      loading:
        attribute: lazy
      display: thumbnail
      link_text: '[media:thumbnail:medium]'
      image_style: large
      colorbox_gallery: none
      colorbox_gallery_custom: ''
      colorbox_caption: custom
      colorbox_caption_custom: ''
    third_party_settings: {  }
    weight: 0
    region: content
  name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 2
    region: content
hidden:
  addtoany: true
  created: true
  langcode: true
  search_api_excerpt: true
  thumbnail: true
  uid: true
