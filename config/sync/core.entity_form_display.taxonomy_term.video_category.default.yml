uuid: bd58e655-6c11-4a95-8cb5-7597a9230c1a
langcode: en
status: true
dependencies:
  config:
    - taxonomy.vocabulary.video_category
  module:
    - path
    - text
id: taxonomy_term.video_category.default
targetEntityType: taxonomy_term
bundle: video_category
mode: default
content:
  description:
    type: text_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  simple_sitemap:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 100
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  translation:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  publish_on: true
  publish_state: true
  unpublish_on: true
  unpublish_state: true
