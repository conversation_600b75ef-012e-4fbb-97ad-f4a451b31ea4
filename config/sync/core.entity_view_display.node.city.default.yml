uuid: be3d4454-68b7-41a8-aca2-b46eec6a318c
langcode: en
status: true
dependencies:
  config:
    - field.field.node.city.field_average_rental_price
    - field.field.node.city.field_average_salary
    - field.field.node.city.field_business_environment
    - field.field.node.city.field_region
    - node.type.city
  module:
    - cshs
    - entity_reference_revisions
    - user
id: node.city.default
targetEntityType: node
bundle: city
mode: default
content:
  field_average_rental_price:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_average_salary:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 4
    region: content
  field_business_environment:
    type: entity_reference_revisions_entity_view
    label: hidden
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 5
    region: content
  field_region:
    type: cshs_group_by_root
    label: hidden
    settings:
      linked: false
      reverse: false
      sort: none
      depth: 1
      last_child: false
    third_party_settings: {  }
    weight: 2
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  toc_js:
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  langcode: true
  node_read_time: true
  private_message_link: true
  search_api_excerpt: true
