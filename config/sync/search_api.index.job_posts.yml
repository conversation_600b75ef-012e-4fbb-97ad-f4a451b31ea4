uuid: 383851ec-5880-42b4-9d15-eac5ac6b7f27
langcode: en
status: true
dependencies:
  config:
    - field.storage.group.field_logo
    - field.storage.node.field_gross_net
    - field.storage.node.field_languages
    - field.storage.node.field_place_of_work
    - field.storage.node.field_professional_field
    - field.storage.node.field_salary
    - field.storage.node.field_work_type
    - search_api.server.bwy_solr_server
  module:
    - entitygroupfield
    - group
    - node
    - search_api_solr
    - taxonomy
third_party_settings:
  search_api_solr:
    finalize: false
    commit_before_finalize: false
    commit_after_finalize: false
    debug_finalize: false
    highlighter:
      maxAnalyzedChars: 51200
      fragmenter: gap
      usePhraseHighlighter: true
      highlightMultiTerm: true
      preserveMulti: false
      regex:
        slop: 0.5
        pattern: blank
        maxAnalyzedChars: 10000
      highlight:
        mergeContiguous: false
        requireFieldMatch: false
        snippets: 3
        fragsize: 0
    mlt:
      mintf: 1
      mindf: 1
      maxdf: 0
      maxdfpct: 0
      minwl: 0
      maxwl: 0
      maxqt: 100
      maxntp: 2000
      boost: false
      interestingTerms: none
    term_modifiers:
      slop: 3
      fuzzy: 1
      fuzzy_analyzer: true
    advanced:
      index_prefix: ''
      collection: ''
      timezone: ''
    multilingual:
      limit_to_content_language: false
      include_language_independent: true
      use_language_undefined_as_fallback_language: false
      specific_languages:
        en: '0'
      use_universal_collation: false
id: job_posts
name: 'Job posts'
description: ''
read_only: false
field_settings:
  company_logo:
    label: 'Company logo'
    datasource_id: 'entity:node'
    property_path: 'entitygroupfield:entity:gid:entity:field_logo'
    type: integer
    dependencies:
      config:
        - field.storage.group.field_logo
      module:
        - entitygroupfield
        - group
  company_name:
    label: 'Company name'
    datasource_id: 'entity:node'
    property_path: 'entitygroupfield:entity:gid:entity:label'
    type: string
    dependencies:
      module:
        - entitygroupfield
        - group
  language:
    label: Language
    datasource_id: 'entity:node'
    property_path: 'field_languages:entity:name'
    type: string
    dependencies:
      config:
        - field.storage.node.field_languages
      module:
        - taxonomy
  place_of_work:
    label: 'Place of work'
    datasource_id: 'entity:node'
    property_path: 'field_place_of_work:entity:name'
    type: string
    dependencies:
      config:
        - field.storage.node.field_place_of_work
      module:
        - taxonomy
  professional_field:
    label: 'Professional field'
    datasource_id: 'entity:node'
    property_path: 'field_professional_field:entity:name'
    type: string
    dependencies:
      config:
        - field.storage.node.field_professional_field
      module:
        - taxonomy
  salary:
    label: Salary
    datasource_id: 'entity:node'
    property_path: field_salary
    type: decimal
    dependencies:
      config:
        - field.storage.node.field_salary
  salary_type:
    label: 'Salary type'
    datasource_id: 'entity:node'
    property_path: field_gross_net
    type: string
    dependencies:
      config:
        - field.storage.node.field_gross_net
  status:
    label: Published
    datasource_id: 'entity:node'
    property_path: status
    type: boolean
    dependencies:
      module:
        - node
  tid:
    label: 'Place of work » Taxonomy term » Term ID'
    datasource_id: 'entity:node'
    property_path: 'field_place_of_work:entity:tid'
    type: integer
    dependencies:
      config:
        - field.storage.node.field_place_of_work
      module:
        - taxonomy
  title:
    label: Title
    datasource_id: 'entity:node'
    property_path: title
    type: string
    dependencies:
      module:
        - node
  work_type:
    label: 'Work type'
    datasource_id: 'entity:node'
    property_path: 'field_work_type:entity:name'
    type: string
    dependencies:
      config:
        - field.storage.node.field_work_type
      module:
        - taxonomy
datasource_settings:
  'entity:node':
    bundles:
      default: false
      selected:
        - job_post
    languages:
      default: true
      selected: {  }
processor_settings:
  add_url: {  }
  aggregated_field: {  }
  auto_aggregated_fulltext_field: {  }
  custom_value: {  }
  entity_status: {  }
  entity_type: {  }
  language_with_fallback: {  }
  rendered_item: {  }
  solr_date_range:
    weights:
      preprocess_index: 0
tracker_settings:
  default:
    indexing_order: fifo
options:
  cron_limit: 50
  delete_on_fail: true
  index_directly: true
  track_changes_in_references: true
server: bwy_solr_server
