uuid: 57dbf540-c65a-4ca6-96fa-fea673606264
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.featured
    - field.field.node.news.field_event
    - field.field.node.news.field_formatted_description
    - field.field.node.news.field_gallery
    - field.field.node.news.field_image_media
    - field.field.node.news.field_summary
    - node.type.news
  module:
    - user
id: node.news.featured
targetEntityType: node
bundle: news
mode: featured
content:
  field_image_media:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_summary:
    type: basic_string
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  reading_time:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  field_event: true
  field_formatted_description: true
  field_gallery: true
  langcode: true
  links: true
  node_read_time: true
  private_message_link: true
  search_api_excerpt: true
  toc_js: true
