uuid: 032cb8cd-2875-49ae-aacd-cb4d639e8ccc
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_schedule_slot
    - paragraphs.paragraphs_type.schedule_day
    - paragraphs.paragraphs_type.schedule_slot
  module:
    - entity_reference_revisions
id: paragraph.schedule_day.field_schedule_slot
field_name: field_schedule_slot
entity_type: paragraph
bundle: schedule_day
label: 'Schedule slot'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      schedule_slot: schedule_slot
    negate: 0
    target_bundles_drag_drop:
      schedule_day:
        weight: 4
        enabled: false
      schedule_slot:
        weight: 5
        enabled: true
      social_media_link:
        weight: 6
        enabled: false
field_type: entity_reference_revisions
