uuid: f8cb780e-7bae-4f69-a8ee-abb1ddc8c34d
langcode: en
status: true
dependencies:
  config:
    - field.storage.media.field_category
    - media.type.remote_video
    - taxonomy.vocabulary.video_category
id: media.remote_video.field_category
field_name: field_category
entity_type: media
bundle: remote_video
label: Category
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      video_category: video_category
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
