uuid: bb7633e2-b724-4a00-b178-23760eb76650
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.profile.listing
    - user.role.administrator
    - user.role.company_admin
    - user.role.company_recruiter
    - user.role.talent
  module:
    - profile
    - user
id: talents
label: Talents
module: views
description: ''
tag: ''
base_table: users_field_data
base_field: uid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: Talents
      fields:
        rendered_entity:
          id: rendered_entity
          table: profile
          field: rendered_entity
          relationship: profile
          group_type: group
          admin_label: ''
          entity_type: profile
          plugin_id: rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: true
          empty_zero: false
          hide_alter_empty: true
          view_mode: listing
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            administrator: administrator
            company_admin: company_admin
            company_recruiter: company_recruiter
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'No results found.'
          tokenize: false
      sorts: {  }
      arguments: {  }
      filters:
        status:
          id: status
          table: users_field_data
          field: status
          entity_type: user
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
        roles_target_id:
          id: roles_target_id
          table: user__roles
          field: roles_target_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: roles
          plugin_id: user_roles
          operator: or
          value:
            talent: talent
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
        field_talent_profile_consent_value:
          id: field_talent_profile_consent_value
          table: profile__field_talent_profile_consent
          field: field_talent_profile_consent_value
          relationship: profile
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: grid_responsive
        options:
          grouping: {  }
          uses_fields: false
          columns: 4
          cell_min_width: 100
          grid_gutter: 10
          alignment: horizontal
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: true
          replica: false
          query_tags: {  }
      relationships:
        profile:
          id: profile
          table: users_field_data
          field: profile
          relationship: none
          group_type: group
          admin_label: Profile
          entity_type: user
          plugin_id: standard
          required: true
      use_ajax: true
      group_by: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url.query_args
        - user.roles
      tags: {  }
  talents:
    id: talents
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders: {  }
      path: node/%node/talents
      menu:
        type: tab
        title: Talents
        description: ''
        weight: 0
        expanded: false
        menu_name: main
        parent: ''
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url.query_args
        - user.roles
      tags: {  }
