uuid: 2d5325fb-0db4-4687-826b-d6b3025e17bd
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.news
    - field.field.media.remote_video.field_category
    - field.field.media.remote_video.field_description_long
    - field.field.media.remote_video.field_media_oembed_video
    - image.style.thumbnail
    - media.type.remote_video
  module:
    - image
    - user
id: media.remote_video.news
targetEntityType: media
bundle: remote_video
mode: news
content:
  created:
    type: timestamp
    label: hidden
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: long
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 0
    region: content
  thumbnail:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: thumbnail
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 5
    region: content
  uid:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  addtoany: true
  field_category: true
  field_description_long: true
  field_media_oembed_video: true
  langcode: true
  name: true
  search_api_excerpt: true
