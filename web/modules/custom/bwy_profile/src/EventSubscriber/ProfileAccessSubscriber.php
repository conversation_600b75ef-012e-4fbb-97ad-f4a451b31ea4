<?php

namespace Drupal\bwy_profile\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Drupal\profile\Entity\ProfileInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Session\AccountInterface;

class ProfileAccessSubscriber implements EventSubscriberInterface {

  protected EntityTypeManagerInterface $entityTypeManager;
  protected AccountInterface $currentUser;

  public function __construct(EntityTypeManagerInterface $entity_type_manager, AccountInterface $current_user) {
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $current_user;
  }

  public static function getSubscribedEvents(): array {
    return [
      KernelEvents::REQUEST => ['onKernelRequest'],
    ];
  }

  public function onKernelRequest(RequestEvent $event): void {
    $request = $event->getRequest();
    $route_name = $request->attributes->get('_route');

    if ($route_name === 'entity.profile.canonical') {
      /** @var \Drupal\profile\Entity\ProfileInterface $profile */
      $profile = $request->attributes->get('profile');
      if (!$profile instanceof ProfileInterface) {
        return;
      }

      // Allow the owner to view their own profile.
      if ($profile->getOwnerId() === $this->currentUser->id()) {
        return;
      }

      // Allow administrators.
      if (in_array('administrator', $this->currentUser->getRoles())) {
        return;
      }

      // Restrict Recruiters to consented Talent profiles only.
      if ($profile->bundle() === 'talent' && in_array('company_recruiter', $this->currentUser->getRoles())) {
        if ($profile->get('field_talent_profile_consent')->value !== '1') {
          throw new AccessDeniedHttpException('This profile is not accessible without consent.');
        }
        return;
      }

      // Deny everyone else.
      throw new AccessDeniedHttpException();
    }
  }
}
