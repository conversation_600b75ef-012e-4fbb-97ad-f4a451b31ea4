<?php

namespace Drupal\bwy_profile\Plugin\Block;

use Drupal\Core\Block\BlockBase;
use <PERSON>upal\Core\Session\AccountInterface;
use <PERSON>upal\Core\Access\AccessResult;
use Drupal\user\Entity\User;
use Drupal\profile\Entity\Profile;

/**
 * Provides a 'Profile Menu' block.
 *
 * @Block(
 *   id = "bwy_profile_menu_block",
 *   admin_label = @Translation("Profile Menu Block"),
 *   category = @Translation("Custom")
 * )
 */
class BwyProfileMenuBlock extends BlockBase {

  /**
   * {@inheritdoc}
   */
  public function build() {
    $current_user = \Drupal::currentUser();
    $uid = $current_user->id();
    $account = User::load($uid);

    $profile_storage = \Drupal::entityTypeManager()->getStorage('profile');
    $profiles = $profile_storage->loadByProperties([
      'uid' => $uid,
      'type' => 'talent',
    ]);
    $profile = !empty($profiles) ? reset($profiles) : NULL;
    $profile_id = $profile ? $profile->id() : NULL;

    $first_name = '';
    $last_name = '';
    if ($profile) {
      $first_name = $profile->get('field_first_name')->value ?? '';
      $last_name = $profile->get('field_last_name')->value ?? '';
    }

    return [
      '#theme' => 'profile_menu_block',
      '#username' => $account->getDisplayName(),
      '#uid' => $uid,
      '#profile_id' => $profile_id,
      '#first_name' => $first_name,
      '#last_name' => $last_name,
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheMaxAge() {
    return 0;
  }

  /**
   * {@inheritdoc}
   */
  public function blockAccess(AccountInterface $account) {
    return AccessResult::allowedIf($account->isAuthenticated());
  }
}
