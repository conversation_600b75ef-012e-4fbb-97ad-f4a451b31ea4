<?php

namespace Drupal\bwy_profile\Routing;

use S<PERSON>fony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Dr<PERSON>al\user\UserInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;

class UserViewRedirectSubscriber implements EventSubscriberInterface {

  protected EntityTypeManagerInterface $entityTypeManager;

  public function __construct(EntityTypeManagerInterface $entity_type_manager) {
    $this->entityTypeManager = $entity_type_manager;
  }

  public static function getSubscribedEvents(): array {
    return [
      KernelEvents::REQUEST => ['onKernelRequest', 30],
    ];
  }

  public function onKernelRequest(RequestEvent $event): void {
    $request = $event->getRequest();

    // Only intercept the user view route.
    if ($request->attributes->get('_route') === 'entity.user.canonical') {
      /** @var \Drupal\user\UserInterface $user */
      $user = $request->attributes->get('user');
      if (!$user instanceof UserInterface) {
        return;
      }

      // Load the default profile of type 'talent'.
      $profiles = $this->entityTypeManager->getStorage('profile')
        ->loadByProperties([
          'uid' => $user->id(),
          'type' => 'talent',
        ]);

      $profile = reset($profiles);

      // If a profile exists, redirect to it.
      if ($profile) {
        $url = $profile->toUrl()->toString();
        $event->setResponse(new RedirectResponse($url));
      }
    }
  }
}
