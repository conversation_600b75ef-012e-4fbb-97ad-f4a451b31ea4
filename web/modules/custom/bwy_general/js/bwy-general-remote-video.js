/**
 * @file
 * BWY General Remote Video integration.
 */

(function ($) {
  Drupal.behaviors.bwyGeneralRemoteVideo = {
    attach: function (context, settings) {
      if (typeof $.colorbox !== 'function' || typeof settings.colorbox === 'undefined') {
        return;
      }

      // The colorbox library uses jQuery.isFunction().
      // This function was removed in jQuery 3.3.0.
      // This is a workaround to avoid fixing the library.
      if (!$.isFunction) {
        $.isFunction = function (obj) {
          return typeof obj === 'function' || false;
        };
      }

      if (settings.colorbox.mobiledetect && window.matchMedia) {
        // Disable Colorbox for small screens.
        var mq = window.matchMedia('(max-device-width: ' + settings.colorbox.mobiledevicewidth + ')');
        if (mq.matches) {
          $.colorbox.remove();
          return;
        }
      }

      // // Set default dimensions for video
      // settings.colorbox.width = '80%';
      // settings.colorbox.height = '80%';
      // settings.colorbox.maxWidth = '1280px';
      // settings.colorbox.maxHeight = '720px';
      // // settings.colorbox.scrolling = false;
      // // settings.colorbox.fixed = true;
      // settings.colorbox.iframe = true;

      settings.colorbox.rel = function () {
        return $(this).data('colorbox-gallery');
      };

      settings.colorbox.html = function () {
        return $(this).data('colorbox-media-video-modal');
      };

      once('init-colorbox', '.colorbox-media-video', context).forEach(element => {
        $(element).colorbox(settings.colorbox);
      });
    }
  };
})(jQuery);
