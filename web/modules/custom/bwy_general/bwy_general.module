<?php

use Drupal\views\ViewExecutable;

/**
 * @file
 * Contains bwy_general.module.
 */

/**
 * Implements hook_theme().
 */
function bwy_general_theme() {
  return [
    'bwy_general_remote_video_formatter' => [
      'variables' => [
        'remote_video' => NULL,
        'thumb' => NULL,
        'entity' => NULL,
        'settings' => NULL,
      ],
      'file' => 'bwy_general.theme.inc',
    ],
  ];
}

/**
 * Implements hook_views_pre_view().
 */
function bwy_general_views_pre_view(ViewExecutable $view, $display_id, array &$args) {
  if ($view->id() == 'job_posts' && $display_id == 'jobs_by_city') {
    $node = \Drupal::routeMatch()->getParameter('node');
    if ($node && $node->hasField('field_region') && !$node->get('field_region')->isEmpty()) {
      $args[0] = $node->get('field_region')->target_id;
    }
  }
}

/**
 * Implements hook_views_pre_render().
 */
function bwy_general_views_pre_render(ViewExecutable $view) {
  if ($view->id() == 'job_posts' && $view->current_display == 'jobs_by_city') {
    $node = \Drupal::routeMatch()->getParameter('node');
    if ($node && $node->hasField('field_region') && !$node->get('field_region')->isEmpty()) {
      $tid = $node->get('field_region')->target_id;
      $term = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->load($tid);
      if ($term) {
        $view->setTitle(t('Jobs in @region', ['@region' => $term->label()]));
      }
    }
  }
}
