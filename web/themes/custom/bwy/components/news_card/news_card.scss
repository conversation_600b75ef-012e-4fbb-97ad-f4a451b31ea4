// News Card Component Styles
// Additional styles that complement Tailwind CSS

.news-card {
  // Ensure consistent aspect ratio for images
  .news-image {
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
  }

  // Line clamping for title and summary (fallback for older browsers)
  .news-title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .news-summary {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // Enhanced hover effects
  &:hover {
    .news-image img {
      transform: scale(1.05);
      transition: transform 0.3s ease;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .news-metadata {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;

      .news-meta-left {
        order: 2;
      }

      .news-read-more {
        order: 1;
        align-self: flex-end;
      }
    }
  }
}
