{#
/**
 * @file
 * Template for a News Card component.
 *
 * Available variables:
 * - slots.image: Featured image for the news article
 * - slots.title: News article title
 * - slots.summary: Brief summary or excerpt
 * - slots.date: Publication date
 * - slots.reading_time: Estimated reading time
 * - slots.read_more_link: Link to full article
 * - url: URL to the full news article
 * - heading_level: Heading level for the title (default: 3)
 * - read_more_text: Text for read more link (default: 'Read more')
 */
#}

<div class="news-card bg-white rounded-xl shadow-md overflow-hidden transition-transform hover:scale-105 hover:shadow-lg group">
  {# Featured image section #}
  {% if slots.image %}
    <div class="news-image relative">
      <div class="w-full h-48 overflow-hidden">
        {{ slots.image }}
      </div>
      {# Optional overlay gradient for better text readability if needed #}
      <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
    </div>
  {% endif %}

  {# Content section #}
  <div class="news-content p-4">
    {# Title #}
    {% if slots.title %}
      {% set heading_tag = 'h' ~ (heading_level|default(3)) %}
      <{{ heading_tag }} class="news-title text-lg font-bold text-gray-900 mb-2 leading-tight group-hover:text-blue-600 transition-colors">
        {% if url %}
          <a href="{{ url }}" class="text-inherit hover:text-blue-600 no-underline block">
            {{ slots.title }}
          </a>
        {% else %}
          {{ slots.title }}
        {% endif %}
      </{{ heading_tag }}>
    {% endif %}

    {# Summary #}
    {% if slots.summary %}
      <div class="news-summary text-gray-600 text-sm mb-4 leading-relaxed">
        {{ slots.summary }}
      </div>
    {% endif %}

    {# Metadata footer #}
    <div class="news-metadata flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100">
      <div class="news-meta-left flex items-center space-x-4">
        {# Publication date #}
        {% if slots.date %}
          <span class="news-date flex items-center">
            <svg class="w-3 h-3 mr-1 opacity-70" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
            </svg>
            {{ slots.date }}
          </span>
        {% endif %}

        {# Reading time #}
        {% if slots.reading_time %}
          <span class="news-reading-time flex items-center">
            <svg class="w-3 h-3 mr-1 opacity-70" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
            </svg>
            {{ slots.reading_time }}
          </span>
        {% endif %}
      </div>

      {# Read more link #}
      {% if url %}
        <div class="news-read-more">
          <a href="{{ url }}" class="text-blue-600 hover:text-blue-800 font-medium transition-colors text-xs">
            {{ read_more_text|default('Read more') }} →
          </a>
        </div>
      {% endif %}
    </div>
  </div>
</div>
