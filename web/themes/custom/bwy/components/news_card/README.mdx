# News Card

A reusable component for displaying news articles in a card format with featured image, title, summary, and metadata.

## Usage

### Basic Usage

```twig
{%
  include 'bwy:news_card' with {
    slots: {
      image: image_content,
      title: 'News article title',
      summary: 'Brief summary of the news article...',
      date: '25 March 2025',
      reading_time: '5 minutes',
    },
    url: '/news/article-url',
    heading_level: 3,
    read_more_text: 'Read more'
  }
%}
```

## Customization Options

- **heading_level**: Set the HTML heading level (h2-h6, default: h3)
- **url**: Link to the full article
- **read_more_text**: Customize the "read more" link text
- **All slots are optional**: Component gracefully handles missing content

## Accessibility

- Semantic HTML structure with proper headings
- Alt text support through image slots
- Keyboard navigation friendly
- Screen reader compatible icons and text
