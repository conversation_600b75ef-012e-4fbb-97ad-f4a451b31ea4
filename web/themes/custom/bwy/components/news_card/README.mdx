# News Card

A reusable component for displaying news articles in a card format with featured image, title, summary, and metadata.

## Key Features

- **Featured Image**: Displays a prominent image with hover effects
- **Responsive Design**: Adapts to different screen sizes
- **Hover Effects**: Subtle scale and shadow transitions on hover
- **Metadata Display**: Shows publication date and reading time with icons
- **Customizable Links**: Configurable read more text and URL
- **Accessibility**: Proper heading levels and semantic markup

## Design Elements

- **Card Layout**: White background with rounded corners and shadow
- **Typography**: Clear hierarchy with bold titles and readable body text
- **Color Scheme**: Uses theme colors (blue for links, gray for text)
- **Icons**: SVG icons for date and reading time
- **Hover States**: Enhanced visual feedback on interaction

## Usage

### Basic Usage

```twig
{%
  include 'bwy:news_card' with {
    slots: {
      image: image_content,
      title: 'News Article Title',
      summary: 'Brief summary of the news article...',
      date: '25 Март 2025',
      reading_time: '5 мин. четене',
    },
    url: '/news/article-url',
    heading_level: 3,
    read_more_text: 'Виж повече'
  }
%}
```

### In Node Templates

```twig
{# node--news--teaser.html.twig #}
{%
  include 'bwy:news_card' with {
    slots: {
      image: content.field_image_media,
      title: label,
      summary: content.field_summary,
      date: node.getCreatedTime()|date('j M Y'),
      reading_time: reading_time_text,
    },
    url: url,
    heading_level: 3
  }
%}
```

## Customization Options

- **heading_level**: Set the HTML heading level (h2-h6, default: h3)
- **url**: Link to the full article
- **read_more_text**: Customize the "read more" link text
- **All slots are optional**: Component gracefully handles missing content

## Styling

The component uses Tailwind CSS classes and follows the existing theme patterns:

- **Card**: `bg-white rounded-xl shadow-md hover:shadow-lg`
- **Image**: Fixed height with overflow hidden
- **Typography**: Responsive text sizes with proper line clamping
- **Colors**: Theme-consistent blue links and gray text
- **Transitions**: Smooth hover effects

## Accessibility

- Semantic HTML structure with proper headings
- Alt text support through image slots
- Keyboard navigation friendly
- Screen reader compatible icons and text
