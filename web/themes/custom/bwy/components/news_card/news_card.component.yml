$schema: https://git.drupalcode.org/project/drupal/-/raw/10.1.x/core/modules/sdc/src/metadata.schema.json
name: News card
description: 'Displays a news article in a card format with image, title, summary, and metadata.'
group: News components
variants:
  default:
    title: Default
slots:
  image:
    title: Image
    description: 'Featured image for the news article.'
  title:
    title: Title
    description: 'News article title.'
  summary:
    title: Summary
    description: 'Brief summary or excerpt of the news article.'
  date:
    title: Date
    description: 'Publication date of the news article.'
  reading_time:
    title: Reading time
    description: 'Estimated reading time for the article.'
  read_more_link:
    title: Read more link
    description: 'Link to the full article.'
props:
  type: object
  properties:
    heading_level:
      title: 'Heading level'
      type: integer
      enum:
        - 2
        - 3
        - 4
        - 5
        - 6
      'meta:enum':
        2: h2
        3: 'h3 (Default)'
        4: h4
        5: h5
        6: h6
      default: 3
    url:
      title: 'Article URL'
      type: string
      description: 'URL to the full news article.'
    read_more_text:
      title: 'Read more text'
      type: string
      description: 'Text for the read more link.'
      default: 'Read more'
