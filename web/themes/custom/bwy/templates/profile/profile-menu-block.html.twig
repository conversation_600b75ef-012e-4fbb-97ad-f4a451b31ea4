{#
/**
 * @file
 * Default theme implementation to display a profile menu block.
 *
 * Available variables:
 * - username: The display name of the current user.
 * - uid: The user ID of the current user.
 * - profile_id: The profile ID of the current user's profile.
 * - first_name: The user's first name from their profile.
 * - last_name: The user's last name from their profile.
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    'profile-menu',
  ]
%}
<div{{ attributes.addClass(classes) }}>
  <div class="profile-menu__user-name">
    {% if first_name or last_name %}
      {{ first_name }} {{ last_name }}
    {% else %}
      {{ username }}
    {% endif %}
  </div>

  <nav class="profile-menu__navigation">
    <ul class="profile-menu__links">
      <li class="profile-menu__link-item">
        <a href="{{ path('entity.profile.canonical', {'profile': profile_id}) }}" class="profile-menu__link">{{ 'Profile'|t }}</a>
      </li>
      <li class="profile-menu__link-item">
        <a href="{{ path('private_message.private_message_page') }}" class="profile-menu__link">{{ 'Messages'|t }}</a>
      </li>
      <li class="profile-menu__link-item">
        <a href="{{ path('entity.user.edit_form', {'user': uid}) }}" class="profile-menu__link">{{ 'Settings'|t }}</a>
      </li>
      <li class="profile-menu__link-item">
        <a href="{{ path('user.logout') }}" class="profile-menu__link">{{ 'Logout'|t }}</a>
      </li>
    </ul>
  </nav>

  <div class="profile-menu__edit-button">
    <a href="{{ path('entity.profile.edit_form', {'profile': profile_id}) }}" class="button button--primary">{{ 'Edit Profile'|t }}</a>
  </div>
</div>
