{#
/**
 * @file
 * Theme override to display a news node in teaser view mode.
 *
 * This template uses the news_card SDC component to render news articles
 * in a consistent card format.
 *
 * Available variables:
 * - node: The node entity with limited access to object properties and methods.
 * - label: The title of the node.
 * - content: All node items.
 * - url: Direct URL of the current node.
 * - date: Themed creation date field.
 * - view_mode: View mode; for example, "teaser" or "full".
 * - teaser: Flag for the teaser state.
 * - attributes: HTML attributes for the containing element.
 *
 * @see template_preprocess_node()
 */
#}

{# Format the publication date #}
{% set formatted_date = node.getCreatedTime()|date('j M Y') %}

{# Get reading time if available #}
{% set reading_time_text = null %}
{% if content.node_read_time %}
  {% set reading_time_text = content.node_read_time %}
{% endif %}

{# Include the news card component #}
{%
  include 'bwy:news_card' with {
    slots: {
      image: content.field_image_media,
      title: label,
      summary: content.field_summary,
      date: formatted_date,
      reading_time: reading_time_text,
    },
    url: url,
    heading_level: 3,
    read_more_text: 'Виж повече'
  }
%}
